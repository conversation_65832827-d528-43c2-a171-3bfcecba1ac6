"""
Enhanced SEC Data Provider using edgartools

This module provides comprehensive SEC filing data extraction and analysis
using the edgartools library for real-time SEC filing access.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import re
from pathlib import Path
import pickle
import os

# Import edgartools components
from edgar import Company, Filing, set_identity
# from edgar.entities import EntityFilings

from config import Config
from utils.logging_utils import get_logger


# Set identity for SEC API access
set_identity("FinMAS Enhanced Financial <NAME_EMAIL>")


@dataclass
class SECFilingData:
    """Represents SEC filing data with extracted metrics"""
    ticker: str
    form_type: str
    filing_date: str
    accession_number: str
    timestamp: str
    period_end_date: Optional[str] = None
    fiscal_year: Optional[int] = None
    fiscal_quarter: Optional[int] = None
    revenue: Optional[float] = None
    net_income: Optional[float] = None
    total_assets: Optional[float] = None
    total_liabilities: Optional[float] = None
    shareholders_equity: Optional[float] = None
    cash_and_equivalents: Optional[float] = None
    operating_cash_flow: Optional[float] = None
    free_cash_flow: Optional[float] = None
    research_development: Optional[float] = None
    selling_general_admin: Optional[float] = None
    cost_of_revenue: Optional[float] = None
    segment_data: Optional[Dict[str, Any]] = None
    risk_factors: Optional[List[str]] = None
    management_discussion: Optional[str] = None


@dataclass
class FinancialSegment:
    """Represents business segment financial data"""
    segment_name: str
    revenue: Optional[float]
    operating_income: Optional[float]
    assets: Optional[float]
    geographic_region: Optional[str]
    growth_rate: Optional[float]


@dataclass
class ExecutiveChange:
    """Represents executive leadership changes"""
    name: str
    position: str
    change_type: str  # "appointment", "resignation", "termination"
    effective_date: str
    reason: Optional[str]


class EnhancedSECDataProvider:
    """Enhanced SEC data provider with comprehensive filing analysis"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        
        # Supported filing types for comprehensive analysis
        self.supported_forms = ["10-K", "10-Q", "8-K", "DEF 14A", "20-F"]
        
        self.logger.logger.info("Enhanced SEC data provider initialized")
    
    async def get_latest_filing(self, ticker: str, form_type: str = "10-K") -> Optional[SECFilingData]:
        """Get the latest SEC filing for a company"""
        try:
            self.logger.logger.info(f"Fetching latest {form_type} filing for {ticker}")
            
            company = Company(ticker)
            filings = company.get_filings(form=form_type).latest(1)
            
            if not filings:
                self.logger.logger.warning(f"No {form_type} filings found for {ticker}")
                return None
            
            filing = filings[0]
            return await self._extract_filing_data(ticker, filing)
            
        except Exception as e:
            self.logger.logger.error(f"[get_latest_filing] Failed to get {form_type} filing for {ticker}: {str(e)} | Function: get_latest_filing | Description: Error occurred while fetching the latest SEC filing from EDGAR database")
            return None
    
    async def get_multiple_filings(
        self, 
        ticker: str, 
        form_types: List[str] = None, 
        count: int = 4
    ) -> List[SECFilingData]:
        """Get multiple recent filings for comprehensive analysis"""
        if form_types is None:
            form_types = ["10-K", "10-Q"]
        
        all_filings = []
        
        try:
            company = Company(ticker)
            
            for form_type in form_types:
                self.logger.logger.info(f"Fetching {count} {form_type} filings for {ticker}")
                
                filings = company.get_filings(form=form_type).latest(count)
                
                for filing in filings:
                    filing_data = await self._extract_filing_data(ticker, filing)
                    if filing_data:
                        all_filings.append(filing_data)
            
            # Sort by filing date (most recent first)
            all_filings.sort(key=lambda x: x.filing_date, reverse=True)
            
            self.logger.logger.info(f"Retrieved {len(all_filings)} filings for {ticker}")
            return all_filings
            
        except Exception as e:
            self.logger.logger.error(f"[get_multiple_filings] Failed to get multiple filings for {ticker}: {str(e)} | Function: get_multiple_filings | Description: Error occurred while fetching multiple recent SEC filings for comprehensive analysis")
            return []
    
    async def _extract_filing_data(self, ticker: str, filing: Filing) -> Optional[SECFilingData]:
        """Extract structured data from SEC filing"""
        try:
            # Get basic filing information
            filing_info = {
                "ticker": ticker,
                "form_type": filing.form,
                "filing_date": filing.filing_date.isoformat() if filing.filing_date else "",
                "accession_number": filing.accession_number,
                "timestamp": datetime.now().isoformat()
            }
            
            # Extract financial data based on form type
            if filing.form in ["10-K", "10-Q"]:
                # Dump the filing content to a local file for debugging
                try:
                    markdown_content = filing.markdown()
                    if filing.form == "10-K":
                        await self._extract_and_save_10k_sections(markdown_content, ticker, filing.form, filing.accession_number)
                    else: # For 10-Q and other forms
                        save_dir = Path("downloaded_filings") / ticker / filing.form
                        save_dir.mkdir(parents=True, exist_ok=True)
                        file_path = save_dir / f"{filing.accession_number}.md"
                        with open(file_path, "w", encoding="utf-8") as f:
                            f.write(markdown_content)
                        self.logger.logger.info(f"Successfully dumped filing to {file_path}")

                except Exception as dump_error:
                    self.logger.logger.error(f"Failed to dump filing {filing.accession_number}: {dump_error}")

                financial_data = await self._extract_financial_statements(filing)
                filing_info.update(financial_data)
                
                # Extract segment data
                segment_data = await self._extract_segment_data(filing)

                # Ensure segment_data is a dictionary
                if not isinstance(segment_data, dict):
                    self.logger.logger.warning(f"[_extract_filing_data] _extract_segment_data returned non-dict: {type(segment_data)} - {segment_data} | Function: _extract_filing_data | Description: segment_data should be a dictionary but received different type")
                    segment_data = {}

                filing_info["segment_data"] = segment_data
                
                # Extract risk factors and MD&A
                risk_factors = await self._extract_risk_factors(filing)
                filing_info["risk_factors"] = risk_factors
                
                mda = await self._extract_management_discussion(filing)
                filing_info["management_discussion"] = mda
                
            elif filing.form == "8-K":
                # Extract material events and corporate changes
                events_data = await self._extract_8k_events(filing)
                filing_info.update(events_data)
                
            elif filing.form == "DEF 14A":
                # Extract executive compensation and governance data
                governance_data = await self._extract_proxy_data(filing)
                filing_info.update(governance_data)
            
            return SECFilingData(**filing_info)
            
        except Exception as e:
            self.logger.logger.error(f"[_extract_filing_data] Failed to extract data from filing: {str(e)} | Function: _extract_filing_data | Description: Error occurred while extracting structured data from SEC filing including financial statements, segment data, and risk factors")
            return None
    
    async def _extract_financial_statements(self, filing: Filing) -> Dict[str, Any]:
        """Extract financial statement data from 10-K/10-Q filings"""
        financial_data = {}
        
        try:
            # This is a simplified extraction - in practice, you'd use XBRL parsing
            # or more sophisticated text extraction methods
            
            # Get the filing content
            filing_text = filing.markdown()
            
            # Extract key financial metrics using pattern matching
            # Note: This is a basic implementation - production would use XBRL data
            
            # Revenue patterns
            revenue_patterns = [
                r"Total\s+(?:net\s+)?revenues?\s*[\$\s]*([0-9,]+)",
                r"Net\s+sales\s*[\$\s]*([0-9,]+)",
                r"Total\s+revenue\s*[\$\s]*([0-9,]+)"
            ]
            
            for pattern in revenue_patterns:
                match = re.search(pattern, filing_text, re.IGNORECASE)
                if match:
                    financial_data["revenue"] = self._parse_financial_number(match.group(1))
                    break
            
            # Net income patterns
            income_patterns = [
                r"Net\s+income\s*[\$\s]*([0-9,]+)",
                r"Net\s+earnings\s*[\$\s]*([0-9,]+)"
            ]
            
            for pattern in income_patterns:
                match = re.search(pattern, filing_text, re.IGNORECASE)
                if match:
                    financial_data["net_income"] = self._parse_financial_number(match.group(1))
                    break
            
            # Extract period information
            period_match = re.search(r"period\s+ended?\s+([A-Za-z]+\s+\d{1,2},?\s+\d{4})", filing_text, re.IGNORECASE)
            if period_match:
                financial_data["period_end_date"] = period_match.group(1)
            
        except Exception as e:
            self.logger.logger.error(f"[_extract_financial_statements] Error extracting financial statements: {str(e)} | Function: _extract_financial_statements | Description: Error occurred while extracting financial statement data from 10-K/10-Q filings using pattern matching")

        return financial_data
    
    async def _extract_and_save_10k_sections(self, markdown_content: str, ticker: str, form_type: str, accession_number: str):
        """Extracts sections from a 10-K filing, converts to Markdown, and saves them."""
        try:
            content = markdown_content

            output_root = Path("downloaded_filings") / ticker / form_type / accession_number
            output_root.mkdir(parents=True, exist_ok=True)

            # Regex to find section headers like '## ITEM 7.'
            item_pattern = re.compile(
                r"(?im)^##\s+(ITEM|PART)\s+([\w\d\.]+)"
            )

            # Find all item headers
            all_matches = list(item_pattern.finditer(content))

            # Filter for unique items, keeping the last occurrence to avoid TOC entries.
            unique_matches_map = {}
            for match in all_matches:
                item_type = match.group(1).strip().upper()
                item_id_raw = match.group(2).strip()
                # Normalize ID, removing trailing dots
                item_id = item_id_raw.upper().rstrip('.')
                unique_key = f"{item_type}_{item_id}"
                unique_matches_map[unique_key] = match

            matches = sorted(list(unique_matches_map.values()), key=lambda m: m.start())

            if not matches:
                self.logger.logger.warning(f"Could not find any sections in {accession_number}. Saving as a single file.")
                file_path = output_root / "full_filing.md"
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return

            num_extracted = 0
            # Iterate through matches to extract sections
            for i in range(len(matches)):
                current_match = matches[i]

                # The start of the content is after the ITEM/PART line
                section_start_pos = current_match.end()

                # Determine the end of the section
                if i + 1 < len(matches):
                    next_match = matches[i+1]
                    section_content = content[section_start_pos:next_match.start()]
                else:
                    # Last section goes to the end of the document
                    section_content = content[section_start_pos:]

                # Find the section title from the first '##' header in the content
                title_match = re.search(r"^\s*##\s*(.*)", section_content.strip(), re.M)

                item_or_part = current_match.group(1).strip().upper()
                section_identifier_raw = current_match.group(2).strip().upper().rstrip('.')
                section_identifier = f"{item_or_part}_{section_identifier_raw.replace('.', '')}"
                
                if title_match:
                    section_title = title_match.group(1).strip()
                else:
                    # Fallback if no '##' title is found, use the line of the ITEM match
                    line_end = content.find('\n', current_match.start())
                    if line_end == -1:
                        line_end = len(content)
                    item_line_text = content[current_match.start():line_end].strip()
                    # Clean up the line to get a title
                    section_title = re.sub(r'^##\s*(ITEM|PART)\s*[\w\d\.]+\s*', '', item_line_text, flags=re.IGNORECASE).strip()
                    if not section_title:
                        section_title = f"{item_or_part} {section_identifier_raw}"
                
                # Sanitize folder name
                folder_name = f"{section_identifier}_{section_title}"
                folder_name = re.sub(r'[^\w\d\-_ .]', '', folder_name).replace(' ', '_').replace('__', '_')
                
                section_dir = output_root / folder_name
                section_dir.mkdir(exist_ok=True)
                
                file_path = section_dir / "section.md"
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"# {item_or_part} {section_identifier_raw}: {section_title}\n\n")
                    f.write(section_content.strip())
                
                num_extracted += 1

            self.logger.logger.info(f"Success: Extracted {num_extracted} sections into '{output_root}'")

        except Exception as e:
            self.logger.logger.error(f"Failed to extract and save 10-K sections for {accession_number}: {e}")
    
    async def _extract_segment_data(self, filing: Filing) -> Dict[str, Any]:
        """Extract comprehensive business segment data with enhanced Azure/cloud detection"""
        segment_data = {}

        try:
            filing_text = filing.markdown()

            # Enhanced segment detection patterns (super dynamic)
            segment_patterns = [
                # Standard segment reporting
                r"segment\s+(?:information|reporting|results|revenue)(.*?)(?:note\s+\d+|consolidated|total|geographic)",
                # Business segment patterns
                r"(?:business\s+)?segment[s]?\s+(?:results|performance|revenue)(.*?)(?:consolidated|total|geographic|note)",
                # Revenue by product/service/segment
                r"revenue\s+by\s+(?:product|service|segment|business\s+unit)(.*?)(?:cost\s+of|operating|total|consolidated)",
                # Product/service line revenue
                r"(?:product|service)\s+line\s+revenue(.*?)(?:cost\s+of|operating|total)",
                # General cloud/technology segment patterns (dynamic)
                r"(?:cloud\s+(?:services|platform|computing)|technology\s+services|digital\s+services)(.*?)(?:revenue|operating|income|cost)",
                # Operating segment patterns
                r"operating\s+segment[s]?\s+(?:results|performance)(.*?)(?:consolidated|total|note)",
                # Reportable segment patterns
                r"reportable\s+segment[s]?(.*?)(?:consolidated|reconciliation|total)",
            ]

            segments_found = []

            for pattern in segment_patterns:
                matches = re.finditer(pattern, filing_text, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    segment_text = match.group(1)[:5000]  # Limit text length

                    # Extract specific segment revenue data
                    segment_revenues = self._extract_segment_revenues(segment_text)
                    self.logger.logger.debug(f"[_extract_segment_data] _extract_segment_revenues returned: {type(segment_revenues)} with {len(segment_revenues) if isinstance(segment_revenues, list) else 'N/A'} items")

                    # Ensure segment_revenues is a list and contains only dictionaries
                    if isinstance(segment_revenues, list):
                        for i, revenue_data in enumerate(segment_revenues):
                            self.logger.logger.debug(f"[_extract_segment_data] Processing revenue_data {i}: type={type(revenue_data)}, content={revenue_data}")
                            if isinstance(revenue_data, dict):
                                segments_found.append(revenue_data)
                            elif isinstance(revenue_data, tuple):
                                self.logger.logger.error(f"[_extract_segment_data] TUPLE DETECTED in segment revenues at index {i}: {revenue_data} | Function: _extract_segment_data | Description: Found tuple in segment revenues - this could be the source of the error")
                                # Try to convert tuple to dict
                                try:
                                    if len(revenue_data) >= 2:
                                        converted_dict = {
                                            "name": revenue_data[0],
                                            "revenue": revenue_data[1],
                                            "unit": "USD",
                                            "source": "Converted from Tuple in _extract_segment_data"
                                        }
                                        segments_found.append(converted_dict)
                                        self.logger.logger.info(f"[_extract_segment_data] Successfully converted tuple to dict: {converted_dict}")
                                    else:
                                        self.logger.logger.error(f"[_extract_segment_data] Cannot convert tuple with insufficient elements: {revenue_data}")
                                except Exception as convert_error:
                                    self.logger.logger.error(f"[_extract_segment_data] Failed to convert tuple to dict: {convert_error}")
                            else:
                                self.logger.logger.warning(f"[_extract_segment_data] Skipping non-dict segment revenue data: {type(revenue_data)} - {revenue_data} | Function: _extract_segment_data | Description: segment revenue data should be dictionary but received different type")
                    else:
                        self.logger.logger.warning(f"[_extract_segment_data] _extract_segment_revenues returned non-list: {type(segment_revenues)} | Function: _extract_segment_data | Description: _extract_segment_revenues should return a list but returned different type")

            # Remove duplicates and consolidate
            unique_segments = {}
            for i, segment in enumerate(segments_found):
                self.logger.logger.debug(f"[_extract_segment_data] Processing segments_found[{i}]: type={type(segment)}, content={segment}")

                # Enhanced tuple detection and conversion
                if isinstance(segment, tuple):
                    self.logger.logger.error(f"[_extract_segment_data] TUPLE DETECTED in segments_found at index {i}: {segment} | Function: _extract_segment_data | Description: Found tuple in segments_found - this is likely the source of the 'tuple' object has no attribute 'get' error")
                    # Try to convert tuple to dict
                    try:
                        if len(segment) >= 2:
                            segment = {
                                "name": segment[0],
                                "revenue": segment[1],
                                "unit": "USD",
                                "source": "Converted from Tuple in unique_segments processing"
                            }
                            self.logger.logger.info(f"[_extract_segment_data] Successfully converted tuple to dict in unique_segments: {segment}")
                        else:
                            self.logger.logger.error(f"[_extract_segment_data] Cannot convert tuple with insufficient elements: {segment}")
                            continue
                    except Exception as convert_error:
                        self.logger.logger.error(f"[_extract_segment_data] Failed to convert tuple to dict in unique_segments: {convert_error}")
                        continue

                # Add defensive check to ensure segment is a dictionary
                if not isinstance(segment, dict):
                    self.logger.logger.warning(f"[_extract_segment_data] Skipping invalid segment data: {type(segment)} - {segment} | Function: _extract_segment_data | Description: segment should be a dictionary but received different type")
                    continue

                if 'name' not in segment:
                    self.logger.logger.warning(f"[_extract_segment_data] Skipping segment without name: {segment} | Function: _extract_segment_data | Description: segment dictionary missing required 'name' field")
                    continue

                name = segment['name']
                self.logger.logger.debug(f"[_extract_segment_data] About to call .get() on segment for comparison: type={type(segment)}, content={segment}")

                try:
                    current_revenue = segment.get('revenue', 0)
                    existing_revenue = unique_segments[name].get('revenue', 0) if name in unique_segments else 0

                    if name not in unique_segments or current_revenue > existing_revenue:
                        unique_segments[name] = segment
                        self.logger.logger.debug(f"[_extract_segment_data] Added/updated segment '{name}' in unique_segments")
                except AttributeError as attr_error:
                    self.logger.logger.error(f"[_extract_segment_data] AttributeError when calling .get() on segment in unique_segments: {attr_error} | Segment type: {type(segment)} | Segment content: {segment}")
                    raise

            segment_data["segments"] = list(unique_segments.values())

            # Extract Azure-specific data if Microsoft
            azure_data = await self._extract_azure_specific_data(filing_text)
            if azure_data:
                segment_data["azure_details"] = azure_data

            # Extract forward-looking guidance for segments
            guidance_data = await self._extract_segment_guidance(filing_text)
            if guidance_data:
                segment_data["guidance"] = guidance_data

        except Exception as e:
            self.logger.logger.error(f"[_extract_segment_data] Error extracting segment data: {str(e)} | Function: _extract_segment_data | Description: Error occurred while extracting comprehensive business segment data with enhanced cloud detection from SEC filing text")

        return segment_data

    def _extract_segment_revenues(self, segment_text: str) -> List[Dict[str, Any]]:
        """Extract segment revenue data from text"""
        segments = []

        revenue_patterns = [
            # Standard format: "Segment revenue was $X billion"
            r"([a-zA-Z\s&\-]+)\s+revenue\s+(?:was|of|totaled|reached)?\s*\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million|mil|bil)",
            # Table format: "Segment    $X.X billion"
            r"([a-zA-Z\s&\-]+)\s+\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million|mil|bil)",
            # Percentage growth format: "Segment revenue grew X% to $Y billion"
            r"([a-zA-Z\s&\-]+)\s+revenue\s+(?:grew|increased|rose)\s+[0-9]+%\s+to\s+\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million|mil|bil)",
            # General segment format: "Segment Name: $X billion"
            r"([A-Za-z\s&\-]+):\s*\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million|mil|bil)",
            # Revenue from segment format: "Revenue from Segment was $X billion"
            r"revenue\s+from\s+([a-zA-Z\s&\-]+)\s+(?:was|totaled|reached)\s*\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million|mil|bil)",
            # Segment generated format: "Segment generated $X billion in revenue"
            r"([a-zA-Z\s&\-]+)\s+generated\s+\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million|mil|bil)\s+in\s+revenue",
        ]

        for pattern in revenue_patterns:
            matches = re.finditer(pattern, segment_text, re.IGNORECASE)
            for match in matches:
                try:
                    if len(match.groups()) < 3:
                        continue

                    segment_name = match.group(1).strip()
                    revenue_amount = match.group(2)
                    revenue_unit = match.group(3).lower()

                    if not segment_name or not revenue_amount or not revenue_unit:
                        continue

                    revenue_value = self._parse_financial_number(f"{revenue_amount} {revenue_unit}")

                    if revenue_value and revenue_value > 0:
                        segment_dict = {
                            "name": self._normalize_segment_name(segment_name),
                            "revenue": revenue_value,
                            "unit": "USD",
                            "source": "SEC Filing"
                        }
                        self.logger.logger.debug(f"[_extract_segment_revenues] Created segment_dict: {segment_dict} (type: {type(segment_dict)})")
                        segments.append(segment_dict)
                        self.logger.logger.debug(f"[_extract_segment_revenues] Added to segments list. Current segments count: {len(segments)}")

                except (AttributeError, IndexError) as e:
                    self.logger.logger.warning(f"Skipping invalid match: {str(e)}")
                    continue

        # Handle tuples in the segments list
        self.logger.logger.debug(f"[_extract_segment_revenues] Before tuple handling: {len(segments)} segments")
        for i, segment in enumerate(segments):
            self.logger.logger.debug(f"[_extract_segment_revenues] Checking segment {i}: type={type(segment)}, content={segment}")
            if isinstance(segment, tuple):
                self.logger.logger.error(f"[_extract_segment_revenues] TUPLE DETECTED at index {i}: {segment} | Function: _extract_segment_revenues | Description: Found tuple in segments list - this should not happen as we only create dicts above")
                try:
                    # Convert tuple to dictionary if possible
                    converted_dict = {
                        "name": segment[0],
                        "revenue": segment[1],
                        "unit": "USD",
                        "source": "Converted Tuple in _extract_segment_revenues"
                    }
                    segments[i] = converted_dict
                    self.logger.logger.info(f"[_extract_segment_revenues] Successfully converted tuple to dict: {converted_dict}")
                except IndexError:
                    self.logger.logger.warning(f"[_extract_segment_revenues] Skipping tuple with insufficient data: {segment}")
                    # Remove the tuple from segments to prevent further processing
                    segments[i] = None

        # Filter out None values that might have been introduced
        final_segments = [s for s in segments if s is not None]
        self.logger.logger.debug(f"[_extract_segment_revenues] Returning {len(final_segments)} segments after tuple handling")
        return final_segments

    def _parse_revenue(self, value_str: str) -> Optional[float]:
        """Parse revenue string into a float, handling various formats."""
        if not value_str or value_str.strip() in ['—', '-', '']:
            return None
        
        value_str = value_str.replace('$', '').replace(',', '').strip()
        
        try:
            # Handle parenthesized negative numbers
            if value_str.startswith('(') and value_str.endswith(')'):
                value_str = '-' + value_str[1:-1]
            
            return float(value_str)
        except (ValueError, TypeError):
            return None

    def _normalize_segment_name(self, name: str) -> str:
        """Normalize segment name for consistency"""
        name = name.lower()

        # Common normalizations
        normalizations = {
            'azure': 'azure',
            'aws': 'aws',
            'office 365': 'office 365',
            'gaming': 'gaming',
            'iphone': 'iphone',
            'mac': 'mac',
            'services': 'services',
            'cloud': 'cloud',
            'intelligent cloud': 'intelligent cloud',
            'productivity and business processes': 'productivity and business processes',
            'more personal computing': 'more personal computing'
        }

        for key, value in normalizations.items():
            if key in name:
                return value

        return name

    async def _extract_azure_specific_data(self, filing_text: str) -> Optional[Dict[str, Any]]:
        """Extract Azure-specific metrics and data"""
        azure_data = {}

        try:
            # Look for Azure-specific mentions
            azure_patterns = [
                r"azure\s+revenue\s+(?:grew|increased|was)\s+([0-9]+)%",
                r"azure\s+(?:consumption|usage)\s+(?:grew|increased)\s+([0-9]+)%",
                r"azure\s+(?:customers|subscribers)\s+(?:grew|increased)\s+([0-9]+)%",
                r"azure\s+(?:annualized|annual)\s+revenue\s+run\s+rate\s+of\s+\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million)"
            ]

            for pattern in azure_patterns:
                matches = re.finditer(pattern, filing_text, re.IGNORECASE)
                for match in matches:
                    if "revenue" in pattern and "grew" in pattern:
                        azure_data["revenue_growth_rate"] = f"{match.group(1)}%"
                    elif "consumption" in pattern:
                        azure_data["consumption_growth_rate"] = f"{match.group(1)}%"
                    elif "customers" in pattern:
                        azure_data["customer_growth_rate"] = f"{match.group(1)}%"
                    elif "run rate" in pattern:
                        azure_data["annual_run_rate"] = self._parse_financial_number(f"{match.group(1)} {match.group(2)}")

            return azure_data if azure_data else None

        except Exception as e:
            self.logger.logger.error(f"[_extract_azure_specific_data] Error extracting Azure-specific data: {str(e)} | Function: _extract_azure_specific_data | Description: Error occurred while extracting Azure-specific metrics and data from SEC filing text")
            return None

    async def _extract_segment_guidance(self, filing_text: str) -> Optional[Dict[str, Any]]:
        """Extract forward-looking guidance for business segments"""
        guidance_data = {}

        try:
            # Look for guidance patterns
            guidance_patterns = [
                r"(?:expect|anticipate|project|forecast)\s+(?:azure|cloud|segment)\s+revenue\s+(?:to\s+)?(?:grow|increase)\s+([0-9]+(?:-[0-9]+)?%)",
                r"(?:fiscal|fy)\s+(?:2024|2025|2026)\s+(?:azure|cloud)\s+revenue\s+(?:expected|projected)\s+(?:to\s+)?(?:be|reach)\s+\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million)",
                r"(?:next\s+year|2025)\s+(?:azure|cloud)\s+(?:growth|revenue)\s+(?:expected|projected)\s+(?:at|to\s+be)\s+([0-9]+(?:-[0-9]+)?%)"
            ]

            for pattern in guidance_patterns:
                matches = re.finditer(pattern, filing_text, re.IGNORECASE)
                for match in matches:
                    if "2025" in match.group(0).lower():
                        if "%" in match.group(1):
                            guidance_data["2025_growth_rate"] = match.group(1)
                        else:
                            guidance_data["2025_revenue_target"] = self._parse_financial_number(f"{match.group(1)} {match.group(2) if len(match.groups()) > 1 else 'billion'}")

            return guidance_data if guidance_data else None

        except Exception as e:
            self.logger.logger.error(f"[_extract_segment_guidance] Error extracting segment guidance: {str(e)} | Function: _extract_segment_guidance | Description: Error occurred while extracting forward-looking guidance for business segments from SEC filing text")
            return None

    async def _extract_risk_factors(self, filing: Filing) -> List[str]:
        """Extract risk factors from filing"""
        risk_factors = []
        
        try:
            filing_text = filing.markdown()
            
            # Find risk factors section
            risk_section = re.search(
                r"risk\s+factors(.*?)(?:item\s+\d+|unresolved|properties)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if risk_section:
                risk_text = risk_section.group(1)
                
                # Extract individual risk factors (simplified)
                risks = re.findall(
                    r"(?:•|\*|-)?\s*([A-Z][^.]*(?:risk|uncertainty|challenge)[^.]*\.)",
                    risk_text,
                    re.IGNORECASE
                )
                
                risk_factors = [risk.strip() for risk in risks[:10]]  # Limit to top 10
        
        except Exception as e:
            self.logger.logger.error(f"[_extract_risk_factors] Error extracting risk factors: {str(e)} | Function: _extract_risk_factors | Description: Error occurred while extracting risk factors from SEC filing using pattern matching")

        return risk_factors
    
    async def _extract_management_discussion(self, filing: Filing) -> Optional[str]:
        """Extract Management Discussion and Analysis section"""
        try:
            filing_text = filing.markdown()
            
            # Find MD&A section
            mda_section = re.search(
                r"management.?s\s+discussion\s+and\s+analysis(.*?)(?:item\s+\d+|quantitative|controls)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if mda_section:
                mda_text = mda_section.group(1)
                # Return first 2000 characters for summary
                return mda_text[:2000].strip()
        
        except Exception as e:
            self.logger.logger.error(f"[_extract_management_discussion] Error extracting MD&A: {str(e)} | Function: _extract_management_discussion | Description: Error occurred while extracting Management Discussion and Analysis section from SEC filing")

        return None
    
    async def _extract_8k_events(self, filing: Filing) -> Dict[str, Any]:
        """Extract material events from 8-K filings"""
        events_data = {}
        
        try:
            filing_text = filing.markdown()
            
            # Look for common 8-K items
            items = {
                "executive_changes": r"item\s+5\.02.*?departure.*?director.*?officer",
                "material_agreements": r"item\s+1\.01.*?material.*?agreement",
                "acquisitions": r"item\s+2\.01.*?completion.*?acquisition",
                "earnings_guidance": r"item\s+2\.02.*?results.*?operations"
            }
            
            for event_type, pattern in items.items():
                if re.search(pattern, filing_text, re.IGNORECASE | re.DOTALL):
                    events_data[event_type] = True
        
        except Exception as e:
            self.logger.logger.error(f"[_extract_8k_events] Error extracting 8-K events: {str(e)} | Function: _extract_8k_events | Description: Error occurred while extracting material events from 8-K filings including executive changes and acquisitions")

        return events_data
    
    async def _extract_proxy_data(self, filing: Filing) -> Dict[str, Any]:
        """Extract executive compensation and governance data from proxy statements"""
        proxy_data = {}
        
        try:
            filing_text = filing.markdown()
            
            # Extract executive compensation summary (simplified)
            comp_section = re.search(
                r"summary\s+compensation\s+table(.*?)(?:grants|outstanding)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            
            if comp_section:
                proxy_data["has_compensation_data"] = True
        
        except Exception as e:
            self.logger.logger.error(f"[_extract_proxy_data] Error extracting proxy data: {str(e)} | Function: _extract_proxy_data | Description: Error occurred while extracting executive compensation and governance data from proxy statements")

        return proxy_data
    
    def _parse_financial_number(self, number_str: str) -> Optional[float]:
        """Parse financial number from string (handles millions, billions)"""
        try:
            # Remove commas and convert to float
            clean_number = re.sub(r'[,$]', '', number_str.strip())
            
            # Handle millions/billions notation
            if 'million' in number_str.lower() or 'mil' in number_str.lower():
                return float(clean_number) * 1_000_000
            elif 'billion' in number_str.lower() or 'bil' in number_str.lower():
                return float(clean_number) * 1_000_000_000
            else:
                return float(clean_number)
        
        except (ValueError, AttributeError):
            return None
    
    async def get_filing_summary(self, ticker: str, form_type: str = "10-K") -> Dict[str, Any]:
        """Get a structured summary of the latest filing"""
        filing_data = await self.get_latest_filing(ticker, form_type)

        if not filing_data:
            return {"error": f"[get_filing_summary] No {form_type} filing found for {ticker} | Function: get_filing_summary | Description: Unable to retrieve SEC filing for summary generation"}

        return {
            "ticker": filing_data.ticker,
            "form_type": filing_data.form_type,
            "filing_date": filing_data.filing_date,
            "period_end_date": filing_data.period_end_date,
            "financial_highlights": {
                "revenue": filing_data.revenue,
                "net_income": filing_data.net_income,
                "total_assets": filing_data.total_assets,
                "free_cash_flow": filing_data.free_cash_flow
            },
            "segment_count": len(filing_data.segment_data.get("segments", [])) if filing_data.segment_data and isinstance(filing_data.segment_data, dict) else 0,
            "risk_factors_count": len(filing_data.risk_factors) if filing_data.risk_factors else 0,
            "has_management_discussion": bool(filing_data.management_discussion),
            "data_source": "SEC EDGAR via edgartools",
            "timestamp": filing_data.timestamp
        }

    async def get_filing_by_period(self, ticker: str, period: str, form_type: str = None) -> Optional[SECFilingData]:
        """Get SEC filing for a specific period (e.g., '2024-Q3', '2023')"""
        try:
            self.logger.logger.info(f"Fetching {period} filing for {ticker}")

            # Parse period to determine form type and approximate filing date
            if 'Q' in period:
                year, quarter = period.split('-Q')
                if quarter == '4':
                    form_type = form_type or "10-K"  # Q4 data typically in annual report
                else:
                    form_type = form_type or "10-Q"
            else:
                year = period
                form_type = form_type or "10-K"

            company = Company(ticker)
            filings = company.get_filings(form=form_type).latest(20)  # Get more filings to find the right period

            # Find filing that matches the requested period
            for filing in filings:
                filing_year = filing.filing_date.year if filing.filing_date else None
                if filing_year and str(filing_year) == year:
                    if form_type == "10-K" or 'Q' not in period:
                        return await self._extract_filing_data(ticker, filing)
                    elif form_type == "10-Q" and 'Q' in period:
                        # Additional logic to match quarter based on filing date
                        filing_month = filing.filing_date.month if filing.filing_date else None
                        quarter_months = {'1': [4, 5], '2': [7, 8], '3': [10, 11]}
                        if quarter in quarter_months and filing_month in quarter_months[quarter]:
                            return await self._extract_filing_data(ticker, filing)

            self.logger.logger.warning(f"No {form_type} filing found for {ticker} in period {period}")
            return None

        except Exception as e:
            self.logger.logger.error(f"[get_filing_by_period] Failed to get {period} filing for {ticker}: {str(e)} | Function: get_filing_by_period | Description: Error occurred while fetching SEC filing for a specific period (e.g., '2024-Q3', '2023')")
            return None

    async def get_complete_document_content(self, ticker: str, form_type: str = "10-K", filing_date: str = "latest") -> Dict[str, Any]:
        """Get complete document content with all sections"""
        try:
            self.logger.logger.info(f"Fetching complete {form_type} document for {ticker}")

            if filing_date == "latest":
                filing_data = await self.get_latest_filing(ticker, form_type)
            else:
                # Find filing by specific date
                company = Company(ticker)
                filings = company.get_filings(form=form_type).latest(10)
                filing_data = None

                for filing in filings:
                    if filing_date in filing.filing_date.isoformat():
                        filing_data = await self._extract_filing_data(ticker, filing)
                        break

                if not filing_data and filings:
                    filing_data = await self._extract_filing_data(ticker, filings[0])

            if not filing_data:
                return {"error": f"[get_complete_document_content] No {form_type} filing found for {ticker} | Function: get_complete_document_content | Description: Unable to retrieve SEC filing for complete document content extraction"}

            # Get the actual filing object for complete content extraction
            company = Company(ticker)
            filings = company.get_filings(form=form_type).latest(1)
            if not filings:
                return {"error": f"[get_complete_document_content] No {form_type} filing found for {ticker} | Function: get_complete_document_content | Description: Unable to retrieve SEC filing object for complete content extraction"}

            filing = filings[0]
            complete_content = await self._extract_complete_document_sections(filing)

            return {
                "ticker": ticker,
                "form_type": form_type,
                "filing_date": filing_data.filing_date,
                "accession_number": filing_data.accession_number,
                "complete_content": complete_content,
                "data_source": "SEC EDGAR Complete Document",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.logger.error(f"[get_complete_document_content] Failed to get complete document for {ticker}: {str(e)} | Function: get_complete_document_content | Description: Error occurred while fetching complete document content with all sections from SEC filing")
            return {"error": f"[get_complete_document_content] {str(e)}"}

    async def _extract_complete_document_sections(self, filing: Filing) -> Dict[str, Any]:
        """Extract all major sections from a complete SEC document"""
        try:
            filing_text = filing.markdown()
            sections = {}

            # Business Description (Item 1 for 10-K)
            business_section = re.search(
                r"item\s+1\.?\s*business(.*?)(?:item\s+1a|item\s+2|risk\s+factors)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if business_section:
                sections["business"] = business_section.group(1)[:5000]  # Limit length

            # Risk Factors (Item 1A)
            risk_section = re.search(
                r"item\s+1a\.?\s*risk\s+factors(.*?)(?:item\s+1b|item\s+2|unresolved)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if risk_section:
                sections["risk_factors"] = risk_section.group(1)[:10000]

            # Properties (Item 2)
            properties_section = re.search(
                r"item\s+2\.?\s*properties(.*?)(?:item\s+3|legal\s+proceedings)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if properties_section:
                sections["properties"] = properties_section.group(1)[:2000]

            # Legal Proceedings (Item 3)
            legal_section = re.search(
                r"item\s+3\.?\s*legal\s+proceedings(.*?)(?:item\s+4|mine\s+safety)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if legal_section:
                sections["legal_proceedings"] = legal_section.group(1)[:3000]

            # Management Discussion and Analysis (Item 7)
            mda_section = re.search(
                r"item\s+7\.?\s*management.?s\s+discussion\s+and\s+analysis(.*?)(?:item\s+7a|item\s+8|quantitative)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if mda_section:
                sections["management_discussion"] = mda_section.group(1)[:15000]

            # Financial Statements (Item 8)
            financial_section = re.search(
                r"item\s+8\.?\s*financial\s+statements(.*?)(?:item\s+9|changes\s+in\s+and\s+disagreements)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if financial_section:
                sections["financial_statements"] = financial_section.group(1)[:20000]

            # Controls and Procedures (Item 9A)
            controls_section = re.search(
                r"item\s+9a\.?\s*controls\s+and\s+procedures(.*?)(?:item\s+9b|item\s+10|other\s+information)",
                filing_text,
                re.IGNORECASE | re.DOTALL
            )
            if controls_section:
                sections["controls_procedures"] = controls_section.group(1)[:3000]

            return sections

        except Exception as e:
            self.logger.logger.error(f"[_extract_complete_document_sections] Error extracting complete document sections: {str(e)} | Function: _extract_complete_document_sections | Description: Error occurred while extracting all major sections from a complete SEC document including business description, risk factors, and financial statements")
            return {}

    async def get_segment_data(self, ticker: str, segment_name: str, period: str = None) -> Dict[str, Any]:
        """Get specific business segment data with temporal filtering"""
        try:
            self.logger.logger.info(f"Fetching {segment_name} segment data for {ticker}")


            self.logger.logger.info(f"Fetching period {period} segment data for {ticker}")

            # Determine which filings to fetch based on period
            if period and period != "latest":
                filing_data = await self.get_filing_by_period(ticker, period)
                filings = [filing_data] if filing_data else []
            else:
                filings = await self.get_multiple_filings(ticker, ["10-K", "10-Q"], 4)

            if not filings:
                return {"error": f"[get_segment_data] No filings found for {ticker} | Function: get_segment_data | Description: Unable to retrieve SEC filings for segment analysis"}

            segment_results = {
                "ticker": ticker,
                "segment_name": segment_name,
                "period_requested": period or "latest",
                "segment_data": [],
                "historical_data": [],
                "guidance": {},
                "data_source": "SEC EDGAR Enhanced"
            }

            # Extract segment data from each filing
            for filing in filings:
                self.logger.logger.debug(f"Processing filing: {filing.form_type} - {filing.filing_date}")
                if hasattr(filing, 'segment_data') and filing.segment_data:
                    self.logger.logger.debug(f"Filing has segment_data of type: {type(filing.segment_data)}")
                    # Ensure segment_data is a dictionary
                    if not isinstance(filing.segment_data, dict):
                        self.logger.logger.warning(f"[get_segment_data] Unexpected segment_data type: {type(filing.segment_data)} - Content: {filing.segment_data} | Function: get_segment_data | Description: filing.segment_data should be a dictionary but received different type")
                        continue

                    # Look for the specific segment
                    if isinstance(filing.segment_data, dict):
                        segments = filing.segment_data.get('segments', [])
                    else:
                        self.logger.logger.warning(f"[get_segment_data] filing.segment_data is not a dict: {type(filing.segment_data)} - {filing.segment_data} | Function: get_segment_data | Description: filing.segment_data should be a dictionary for .get() method access")
                        continue

                    self.logger.logger.debug(f"Found {len(segments)} segments of type: {type(segments)}")
                    if not isinstance(segments, list):
                        self.logger.logger.warning(f"[get_segment_data] Unexpected segments type: {type(segments)} - Content: {segments} | Function: get_segment_data | Description: segments should be a list but received different type")
                        continue

                    for i, segment in enumerate(segments):
                        self.logger.logger.debug(f"Processing segment {i}: type={type(segment)}, content={segment}")

                        # Enhanced logging to catch tuple issues
                        if isinstance(segment, tuple):
                            self.logger.logger.error(f"[get_segment_data] TUPLE DETECTED at segment {i}: {segment} | Function: get_segment_data | Description: Found tuple where dictionary expected - this is the source of the 'tuple' object has no attribute 'get' error")
                            # Try to convert tuple to dict if possible
                            try:
                                if len(segment) >= 2:
                                    segment = {
                                        "name": segment[0],
                                        "revenue": segment[1],
                                        "unit": "USD",
                                        "source": "Converted from Tuple"
                                    }
                                    self.logger.logger.info(f"[get_segment_data] Successfully converted tuple to dict: {segment}")
                                else:
                                    self.logger.logger.error(f"[get_segment_data] Cannot convert tuple with insufficient elements: {segment}")
                                    continue
                            except Exception as convert_error:
                                self.logger.logger.error(f"[get_segment_data] Failed to convert tuple to dict: {convert_error}")
                                continue

                        if not isinstance(segment, dict):
                            self.logger.logger.warning(f"[get_segment_data] Unexpected segment type: {type(segment)} - Content: {segment} | Function: get_segment_data | Description: individual segment should be a dictionary but received different type")
                            continue

                        if 'name' not in segment:
                            self.logger.logger.warning(f"[get_segment_data] Segment missing 'name' field: {segment} | Function: get_segment_data | Description: segment dictionary missing required 'name' field for matching")
                            continue

                        self.logger.logger.debug(f"Checking segment match: '{segment['name']}' vs '{segment_name}'")
                        if self._is_segment_match(segment['name'], segment_name):
                            self.logger.logger.info(f"Found matching segment: {segment['name']} for {segment_name}")

                            # Enhanced logging before .get() calls
                            self.logger.logger.debug(f"About to call .get() on segment of type {type(segment)}: {segment}")

                            try:
                                segment_entry = {
                                    "filing_date": filing.filing_date,
                                    "period_end": filing.period_end_date,
                                    "form_type": filing.form_type,
                                    "revenue": segment.get('revenue'),
                                    "name": segment['name'],
                                    "source": segment.get('source', 'SEC Filing')
                                }
                                segment_results["segment_data"].append(segment_entry)
                                self.logger.logger.debug(f"Successfully added segment entry: {segment_entry}")
                            except AttributeError as attr_error:
                                self.logger.logger.error(f"[get_segment_data] AttributeError when calling .get() on segment: {attr_error} | Segment type: {type(segment)} | Segment content: {segment}")
                                raise

                    # Add segment-specific details if available (dynamic)
                    if isinstance(filing.segment_data, dict):
                        segment_details_key = f"{segment_name.lower()}_details"
                        if filing.segment_data.get(segment_details_key):
                            segment_results[segment_details_key] = filing.segment_data[segment_details_key]

                        # Add guidance data
                        guidance = filing.segment_data.get('guidance')
                        if guidance and isinstance(guidance, dict):
                            segment_results["guidance"].update(guidance)

            # Sort by filing date (most recent first)
            segment_results["segment_data"].sort(
                key=lambda x: x['filing_date'] if x['filing_date'] else datetime.min,
                reverse=True
            )

            return segment_results

        except Exception as e:
            self.logger.logger.error(f"[get_segment_data] Failed to get segment data for {ticker} {segment_name}: {str(e)} | Function: get_segment_data | Description: Error occurred while fetching specific business segment data with temporal filtering from SEC filings")
            return {"error": f"[get_segment_data] {str(e)}"}

    def _is_segment_match(self, segment_name: str, target_segment: str) -> bool:
        """Dynamically check if a segment name matches the target segment."""
        
        target_segment_lower = target_segment.lower()
        # Generate keywords from the target segment
        target_keywords = set(target_segment_lower.split())
        
        # Add common variations
        if "cloud" in target_keywords:
            target_keywords.add("gcp")
            target_keywords.add("google cloud")
        
        segment_name_lower = segment_name.lower()
        
        # Direct match
        if target_segment_lower in segment_name_lower:
            return True
            
        # Keyword-based match
        for keyword in target_keywords:
            if keyword in segment_name_lower:
                return True
        
        # Handle cases like "GCP" for "Google Cloud"
        if "gcp" in target_keywords and "google cloud" in segment_name_lower:
            return True

        return False

    async def get_future_period_analysis(self, ticker: str, target_year: str, segment_name: str = None) -> Dict[str, Any]:
        """
        Analyzes future guidance and management targets for a specific year and segment.
        
        Args:
            ticker: The company ticker symbol
            target_year: The target year for analysis
            segment_name: The specific segment for analysis
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            self.logger.logger.info(f"Fetching {target_year} analysis for {ticker}" + (f" {segment_name}" if segment_name else ""))

            # Get latest filings for guidance
            filings = await self.get_multiple_filings(ticker, ["10-K", "10-Q"], 6)

            if not filings:
                return {"error": f"[get_future_period_analysis] No filings found for {ticker} | Function: get_future_period_analysis | Description: Unable to retrieve SEC filings for future period analysis"}

            future_analysis = {
                "ticker": ticker,
                "target_year": target_year,
                "segment_name": segment_name,
                "guidance_data": {},
                "projections": {},
                "management_targets": {},
                "analyst_estimates": {},
                "data_source": "SEC EDGAR Future Analysis",
                "timestamp": datetime.now().isoformat()
            }

            # Extract guidance from each filing
            for filing in filings:
                if hasattr(filing, 'management_discussion') and filing.management_discussion:
                    guidance = await self._extract_future_guidance(
                        filing.management_discussion,
                        target_year,
                        segment_name
                    )
                    if guidance:
                        future_analysis["guidance_data"][filing.filing_date.isoformat()] = guidance

                # Extract from segment data if available
                if hasattr(filing, 'segment_data') and filing.segment_data and isinstance(filing.segment_data, dict):
                    segment_guidance = filing.segment_data.get('guidance', {})
                    if segment_guidance:
                        future_analysis["projections"].update(segment_guidance)

            # Extract management targets from latest filing
            if filings:
                latest_filing = filings[0]
                targets = await self._extract_management_targets(latest_filing, target_year, segment_name)
                if targets:
                    future_analysis["management_targets"] = targets

            return future_analysis

        except Exception as e:
            self.logger.logger.error(f"[get_future_period_analysis] Failed to get future period analysis for {ticker}: {str(e)} | Function: get_future_period_analysis | Description: Error occurred while extracting future guidance and projections for a specific target year from SEC filings")
            return {"error": f"[get_future_period_analysis] {str(e)}"}

    async def _extract_future_guidance(self, mda_text: str, target_year: str, segment_name: str = None) -> Dict[str, Any]:
        """Extract future guidance from Management Discussion and Analysis"""
        guidance = {}

        try:
            # Create segment-specific patterns if segment name provided
            if segment_name:
                segment_patterns = [
                    rf"(?:fiscal|fy)?\s*{target_year}\s+{segment_name.lower()}\s+(?:revenue|growth)\s+(?:expected|projected|anticipated)\s+(?:to\s+)?(?:be|reach|grow)\s+([0-9]+(?:-[0-9]+)?%|\$[0-9,]+(?:\.[0-9]+)?\s*(?:billion|million))",
                    rf"{segment_name.lower()}\s+(?:revenue|growth)\s+(?:for|in)\s+(?:fiscal|fy)?\s*{target_year}\s+(?:expected|projected)\s+(?:to\s+)?(?:be|reach|grow)\s+([0-9]+(?:-[0-9]+)?%|\$[0-9,]+(?:\.[0-9]+)?\s*(?:billion|million))",
                    rf"(?:expect|anticipate|project)\s+{segment_name.lower()}\s+(?:to\s+)?(?:grow|increase)\s+([0-9]+(?:-[0-9]+)?%)\s+(?:in|for)\s+(?:fiscal|fy)?\s*{target_year}"
                ]
            else:
                segment_patterns = [
                    rf"(?:fiscal|fy)?\s*{target_year}\s+(?:revenue|growth)\s+(?:expected|projected|anticipated)\s+(?:to\s+)?(?:be|reach|grow)\s+([0-9]+(?:-[0-9]+)?%|\$[0-9,]+(?:\.[0-9]+)?\s*(?:billion|million))",
                    rf"(?:revenue|growth)\s+(?:for|in)\s+(?:fiscal|fy)?\s*{target_year}\s+(?:expected|projected)\s+(?:to\s+)?(?:be|reach|grow)\s+([0-9]+(?:-[0-9]+)?%|\$[0-9,]+(?:\.[0-9]+)?\s*(?:billion|million))"
                ]

            for pattern in segment_patterns:
                matches = re.finditer(pattern, mda_text, re.IGNORECASE)
                for match in matches:
                    guidance_value = match.group(1)
                    if '%' in guidance_value:
                        guidance[f"{target_year}_growth_rate"] = guidance_value
                    else:
                        # Parse revenue target
                        revenue_match = re.search(r'\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million)', guidance_value, re.IGNORECASE)
                        if revenue_match:
                            amount = revenue_match.group(1)
                            unit = revenue_match.group(2).lower()
                            guidance[f"{target_year}_revenue_target"] = self._parse_financial_number(f"{amount} {unit}")

            # Look for general forward-looking statements
            forward_patterns = [
                rf"(?:over\s+the\s+next|in\s+the\s+coming)\s+(?:year|years).*?(?:expect|anticipate|project).*?(?:growth|revenue).*?([0-9]+(?:-[0-9]+)?%)",
                rf"(?:long[- ]?term|multi[- ]?year)\s+(?:growth|revenue)\s+(?:target|goal|objective).*?([0-9]+(?:-[0-9]+)?%)"
            ]

            for pattern in forward_patterns:
                matches = re.finditer(pattern, mda_text, re.IGNORECASE)
                for match in matches:
                    guidance["long_term_growth_target"] = match.group(1)

            return guidance if guidance else None

        except Exception as e:
            self.logger.logger.error(f"[_extract_future_guidance] Error extracting future guidance: {str(e)} | Function: _extract_future_guidance | Description: Error occurred while extracting future guidance from Management Discussion and Analysis section")
            return None

    async def _extract_management_targets(self, filing: 'SECFilingData', target_year: str, segment_name: str = None) -> Dict[str, Any]:
        """Extract management targets and strategic goals"""
        targets = {}

        try:
            # Look in management discussion
            if hasattr(filing, 'management_discussion') and filing.management_discussion:
                mda_text = filing.management_discussion

                # Target patterns
                target_patterns = [
                    rf"(?:target|goal|objective).*?{target_year}.*?([0-9]+(?:-[0-9]+)?%|\$[0-9,]+(?:\.[0-9]+)?\s*(?:billion|million))",
                    rf"{target_year}.*?(?:target|goal|objective).*?([0-9]+(?:-[0-9]+)?%|\$[0-9,]+(?:\.[0-9]+)?\s*(?:billion|million))",
                    rf"(?:strategic|financial)\s+(?:target|goal).*?([0-9]+(?:-[0-9]+)?%)"
                ]

                for pattern in target_patterns:
                    matches = re.finditer(pattern, mda_text, re.IGNORECASE)
                    for match in matches:
                        target_value = match.group(1)
                        if '%' in target_value:
                            targets[f"{target_year}_strategic_target"] = target_value
                        else:
                            revenue_match = re.search(r'\$([0-9,]+(?:\.[0-9]+)?)\s*(billion|million)', target_value, re.IGNORECASE)
                            if revenue_match:
                                amount = revenue_match.group(1)
                                unit = revenue_match.group(2).lower()
                                targets[f"{target_year}_revenue_target"] = self._parse_financial_number(f"{amount} {unit}")

            return targets if targets else None

        except Exception as e:
            self.logger.logger.error(f"[_extract_management_targets] Error extracting management targets: {str(e)} | Function: _extract_management_targets | Description: Error occurred while extracting management targets and strategic goals from SEC filing")
            return None

    async def extract_and_save_filing_sections(self, filing: Filing, ticker: str, form_type: str = "10-K") -> Dict[str, str]:
        """
        Extract each section of the filing document using regex and save to separate files.
        
        Args:
            filing: The Filing object to process
            ticker: The company ticker symbol
            form_type: The type of filing (e.g., '10-K', '10-Q')
            
        Returns:
            Dictionary mapping section names to file paths
        """
        try:
            self.logger.logger.info(f"Extracting sections from {form_type} filing for {ticker}")
            
            # Get the full filing text
            filing_text = filing.markdown()
            
            # Define section patterns based on form type
            section_patterns = self._get_section_patterns(form_type)
            
            # Create directory for saving sections
            output_dir = Path(f"output/sec_sections/{ticker}/{form_type}")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Extract and save each section
            section_files = {}
            filing_date = filing.filing_date.strftime("%Y%m%d") if filing.filing_date else "unknown_date"
            
            for section_name, pattern in section_patterns.items():
                section_match = re.search(pattern, filing_text, re.IGNORECASE | re.DOTALL)
                
                if section_match:
                    section_text = section_match.group(1)
                    
                    # Create section object with metadata
                    section_obj = {
                        "ticker": ticker,
                        "form_type": form_type,
                        "filing_date": filing.filing_date,
                        "section_name": section_name,
                        "text": section_text,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Save section to file
                    file_path = output_dir / f"{ticker}_{form_type}_{section_name}.pkl"
                    with open(file_path, 'wb') as f:
                        pickle.dump(section_obj, f)
                    
                    # Also save as text file for easy viewing
                    text_file_path = output_dir / f"{ticker}_{form_type}_{section_name}.txt"
                    with open(text_file_path, 'w', encoding='utf-8') as f:
                        f.write(section_text)
                    
                    section_files[section_name] = str(file_path)
                    self.logger.logger.info(f"Extracted {section_name} section for {ticker} {form_type}")
                else:
                    self.logger.logger.warning(f"Could not find {section_name} section in {ticker} {form_type}")
            
            return section_files
            
        except Exception as e:
            self.logger.logger.error(f"[extract_and_save_filing_sections] Error extracting sections from {form_type} filing for {ticker}: {str(e)} | Function: extract_and_save_filing_sections | Description: Error occurred while extracting and saving individual sections from SEC filing to separate files")
            return {}

    def _get_section_patterns(self, form_type: str) -> Dict[str, str]:
        """
        Get regex patterns for extracting sections based on form type.
        
        Args:
            form_type: The type of filing (e.g., '10-K', '10-Q')
            
        Returns:
            Dictionary mapping section names to regex patterns
        """
        if form_type == "10-K":
            return {
                "business": r"item\s+1\.?\s*business(.*?)(?:item\s+1a|item\s+2|risk\s+factors)",
                "risk_factors": r"item\s+1a\.?\s*risk\s+factors(.*?)(?:item\s+1b|item\s+2|unresolved)",
                "properties": r"item\s+2\.?\s*properties(.*?)(?:item\s+3|legal\s+proceedings)",
                "legal_proceedings": r"item\s+3\.?\s*legal\s+proceedings(.*?)(?:item\s+4|mine\s+safety)",
                "market_info": r"item\s+5\.?\s*market(?:\s+for)?\s+registrant.?s\s+common\s+equity(.*?)(?:item\s+6|selected\s+financial)",
                "management_discussion": r"item\s+7\.?\s*management.?s\s+discussion\s+and\s+analysis(.*?)(?:item\s+7a|item\s+8|quantitative)",
                "financial_statements": r"item\s+8\.?\s*financial\s+statements(.*?)(?:item\s+9|changes\s+in\s+and\s+disagreements)",
                "controls_procedures": r"item\s+9a\.?\s*controls\s+and\s+procedures(.*?)(?:item\s+9b|item\s+10|other\s+information)",
                "directors_officers": r"item\s+10\.?\s*directors(?:,|\s+and)\s+executive\s+officers(.*?)(?:item\s+11|executive\s+compensation)",
                "executive_compensation": r"item\s+11\.?\s*executive\s+compensation(.*?)(?:item\s+12|security\s+ownership)",
                "exhibits": r"item\s+15\.?\s*exhibits(?:,|\s+and)\s+financial\s+statement\s+schedules(.*?)(?:signatures|part\s+iv)"
            }
        elif form_type == "10-Q":
            return {
                "financial_statements": r"item\s+1\.?\s*financial\s+statements(.*?)(?:item\s+2|management)",
                "management_discussion": r"item\s+2\.?\s*management.?s\s+discussion\s+and\s+analysis(.*?)(?:item\s+3|quantitative)",
                "risk_factors": r"item\s+1a\.?\s*risk\s+factors(.*?)(?:item\s+2|unresolved)",
                "legal_proceedings": r"item\s+1\.?\s*legal\s+proceedings(.*?)(?:item\s+1a|risk\s+factors)",
                "controls_procedures": r"item\s+4\.?\s*controls\s+and\s+procedures(.*?)(?:item\s+5|part\s+ii)"
            }
        elif form_type == "8-K":
            return {
                "item_1_01": r"item\s+1\.01.*?(?:entry\s+into|material\s+definitive\s+agreement)(.*?)(?:item\s+1\.02|item\s+1\.03|item\s+1\.04|item\s+2\.01)",
                "item_2_01": r"item\s+2\.01.*?(?:completion\s+of\s+acquisition|disposition\s+of\s+assets)(.*?)(?:item\s+2\.02|item\s+2\.03|item\s+2\.04|item\s+2\.05)",
                "item_2_02": r"item\s+2\.02.*?(?:results\s+of\s+operations\s+and\s+financial\s+condition)(.*?)(?:item\s+2\.03|item\s+2\.04|item\s+2\.05|item\s+2\.06)",
                "item_5_02": r"item\s+5\.02.*?(?:departure\s+of\s+directors|appointment\s+of\s+certain\s+officers)(.*?)(?:item\s+5\.03|item\s+5\.04|item\s+5\.05|item\s+5\.06)",
                "item_7_01": r"item\s+7\.01.*?(?:regulation\s+fd\s+disclosure)(.*?)(?:item\s+7\.02|item\s+8\.01|item\s+9\.01)",
                "item_9_01": r"item\s+9\.01.*?(?:financial\s+statements\s+and\s+exhibits)(.*?)(?:signatures|$)"
            }
        else:
            # Default patterns for other form types
            return {
                "full_text": r"(.*)"  # Capture the entire document
            }

    async def get_filing_sections(self, ticker: str, form_type: str = "10-K") -> Dict[str, Dict[str, Any]]:
        """
        Get sections for a specific filing, extracting them if they don't exist.
        
        Args:
            ticker: The company ticker symbol
            form_type: The type of filing (e.g., '10-K', '10-Q')
            
        Returns:
            Dictionary mapping section names to section objects
        """
        try:
            # Check if sections already exist
            output_dir = Path(f"output/sec_sections/{ticker}/{form_type}")
            if output_dir.exists():
                section_files = list(output_dir.glob(f"{ticker}_{form_type}_*.pkl"))
                if section_files:
                    # Load sections from disk
                    sections = {}
                    for file_path in section_files:
                        section_name = file_path.stem.split('_')[-1]
                        with open(file_path, 'rb') as f:
                            sections[section_name] = pickle.load(f)
                    return sections
            
            # If sections don't exist, fetch filing and extract sections
            self.logger.logger.info(f"No existing sections found for {ticker} {form_type}, extracting new sections")
            company = Company(ticker)
            filings = company.get_filings(form=form_type).latest(1)
            
            if not filings:
                self.logger.logger.warning(f"No {form_type} filings found for {ticker}")
                return {}
            
            filing = filings[0]
            section_files = await self.extract_and_save_filing_sections(filing, ticker, form_type)
            
            # Load and return the newly created sections
            sections = {}
            for section_name, file_path in section_files.items():
                with open(file_path, 'rb') as f:
                    sections[section_name] = pickle.load(f)
            
            return sections
            
        except Exception as e:
            self.logger.logger.error(f"[get_filing_sections] Error getting filing sections for {ticker} {form_type}: {str(e)} | Function: get_filing_sections | Description: Error occurred while loading previously saved filing sections from pickle files")
            return {}

